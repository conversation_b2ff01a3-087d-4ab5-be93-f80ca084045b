const fs = require('fs');
const path = require('path');

// Create a simple SVG icon generator for Naroop
function generateSVGIcon(size, text = 'N') {
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#grad)"/>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-weight="bold" 
        font-size="${size * 0.5}" fill="white">${text}</text>
</svg>`;
}

// Icon sizes needed for iOS and PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
const iconsDir = path.join(__dirname, '..', 'public', 'images', 'icons');

// Ensure icons directory exists
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
}

console.log('🎨 Generating Naroop app icons...');

// Generate SVG icons for each size
iconSizes.forEach(size => {
    const svgContent = generateSVGIcon(size);
    const filename = `icon-${size}x${size}.svg`;
    const filepath = path.join(iconsDir, filename);
    
    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Generated ${filename}`);
});

// Generate shortcut icons
const shortcutIcons = [
    { name: 'shortcut-post.svg', text: '+', size: 96 },
    { name: 'shortcut-stories.svg', text: '📖', size: 96 }
];

shortcutIcons.forEach(({ name, text, size }) => {
    const svgContent = generateSVGIcon(size, text);
    const filepath = path.join(iconsDir, name);
    
    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Generated ${name}`);
});

console.log('🎉 All icons generated successfully!');
console.log('📝 Note: These are placeholder SVG icons. For production, you should:');
console.log('   1. Create proper PNG icons using a design tool');
console.log('   2. Use your actual Naroop logo/branding');
console.log('   3. Ensure icons follow iOS Human Interface Guidelines');
