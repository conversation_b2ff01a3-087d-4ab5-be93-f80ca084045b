import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.naroop.app',
  appName: 'Naroop',
  webDir: 'public',
  server: {
    // For production, update this to your live server URL
    // For development, you can use your local IP or ngrok tunnel
    // url: 'https://your-production-domain.com',
    // cleartext: false

    // For local development (comment out for production):
    url: 'http://localhost:3000',
    cleartext: true
  },
  ios: {
    // iOS-specific configuration
    contentInset: 'always',
    backgroundColor: '#0f172a', // Your dark theme background
    allowsLinkPreview: false,
    scrollEnabled: true,
    // Handle safe areas properly
    webContentsDebuggingEnabled: true, // Enable for development
    // App icon and launch screen will be configured later
    scheme: 'naroop'
  },
  plugins: {
    // Configure built-in plugins
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#0f172a',
      showSpinner: false,
      androidSpinnerStyle: 'large',
      iosSpinnerStyle: 'small',
      spinnerColor: '#f59e0b', // Your orange accent color
      splashFullScreen: true,
      splashImmersive: true
    },
    StatusBar: {
      style: 'dark',
      backgroundColor: '#0f172a'
    },
    Keyboard: {
      resize: 'body',
      style: 'dark',
      resizeOnFullScreen: true
    }
  }
};

export default config;
