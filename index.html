<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>

    <!-- External CSS Files - Optimized Loading Order -->
    <link rel="stylesheet" href="/public/css/variables.css?v=5">
    <link rel="stylesheet" href="/public/css/main.css?v=4">
    <link rel="stylesheet" href="/public/css/layout-unified.css?v=1">
    <link rel="stylesheet" href="/public/css/modern-animations.css?v=2">
    <link rel="stylesheet" href="/public/css/posts.css?v=3">
    <link rel="stylesheet" href="/public/css/forms.css?v=3">
    <link rel="stylesheet" href="/public/css/notifications.css?v=3">
    <link rel="stylesheet" href="/public/css/performance.css?v=4">
    <link rel="stylesheet" href="/public/css/preferences.css?v=3">

    <!-- Google Fonts for Modern Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/public/css/modern-dark.css">
    <link rel="stylesheet" href="/public/css/modal.css">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/public/manifest.json">

    <!-- iOS-specific meta tags for app-like behavior -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Naroop">

    <!-- Theme colors for mobile browsers -->
    <meta name="theme-color" content="#f59e0b">
    <meta name="msapplication-navbutton-color" content="#f59e0b">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
</head>
<body class="modern-dark">
    <!-- Header -->
    <header class="header-modern">
        <div class="logo-container">
            <h1 class="logo">Naroop</h1>
            <div class="search-container">
                <div class="search-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                </div>
                <input type="text" placeholder="Search communities, topics, people..." class="search-input">
            </div>
        </div>

        <div class="user-actions">
            <button class="action-btn" data-modal-target="search-modal">
                <svg class="mobile-search-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
            </button>
            <button class="action-btn" data-modal-target="notifications-modal">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>
            </button>
            <button class="action-btn" data-modal-target="profile-modal">
                <div class="profile-avatar">
                    <img src="https://placehold.co/32x32/4F46E5/ffffff?text=M" alt="User profile">
                </div>
            </button>
        </div>
    </header>

    <!-- Main content area -->
    <main class="main-content-modern">

        <!-- Left sidebar (Navigation) -->
        <aside class="sidebar-modern">
            <nav>
                <ul class="nav-menu">
                    <li>
                        <a href="#" class="nav-item active">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                            <span>Explore</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-item create-post-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                            <span>Create Post</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-item" data-modal-target="trending-modal">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3L9.27 9.27L3 12l6.27 2.73L12 21l2.73-6.27L21 12l-6.27-2.73L12 3z"></path><path d="M3 21L12 12"></path><path d="M21 3L12 12"></path></svg>
                            <span>Trending</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-item" data-modal-target="profile-modal">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                            <span>Profile</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Center content (Stories, Post, Feed) -->
        <section class="feed-modern">
            <!-- Stories section -->
            <div class="card stories-card">
                <div class="card-header">
                    <h2 class="card-title">Stories</h2>
                    <button class="view-all-btn" data-modal-target="stories-modal">View All</button>
                </div>
                <div class="stories-container">
                    <div class="stories-scroll">
                        <!-- Add Story button -->
                        <div class="story-item" onclick="handleAddStory()">
                            <div class="story-avatar-wrapper add-story">
                                <svg class="add-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                            </div>
                            <p class="story-name">Add Story</p>
                        </div>
                        <!-- Story items -->
                        <div class="story-item" onclick="handleStoryClick(1)">
                            <div class="story-avatar-wrapper has-story">
                                <img src="https://placehold.co/100x100/4F46E5/ffffff?text=M" alt="Maya" class="story-avatar">
                            </div>
                            <p class="story-name">Maya</p>
                        </div>
                        <div class="story-item" onclick="handleStoryClick(2)">
                            <div class="story-avatar-wrapper has-story">
                                <img src="https://placehold.co/100x100/3B82F6/ffffff?text=J" alt="John" class="story-avatar">
                            </div>
                            <p class="story-name">John</p>
                        </div>
                        <div class="story-item" onclick="handleStoryClick(3)">
                            <div class="story-avatar-wrapper no-story">
                                <img src="https://placehold.co/100x100/A855F7/ffffff?text=S" alt="Sarah" class="story-avatar">
                            </div>
                            <p class="story-name">Sarah</p>
                        </div>
                        <div class="story-item" onclick="handleStoryClick(4)">
                            <div class="story-avatar-wrapper has-story">
                                <img src="https://placehold.co/100x100/F97316/ffffff?text=A" alt="Alex" class="story-avatar">
                            </div>
                            <p class="story-name">Alex</p>
                        </div>
                        <div class="story-item" onclick="handleStoryClick(5)">
                            <div class="story-avatar-wrapper no-story">
                                <img src="https://placehold.co/100x100/EF4444/ffffff?text=E" alt="Emily" class="story-avatar">
                            </div>
                            <p class="story-name">Emily</p>
                        </div>
                        <div class="story-item" onclick="handleStoryClick(6)">
                            <div class="story-avatar-wrapper has-story">
                                <img src="https://placehold.co/100x100/10B981/ffffff?text=D" alt="David" class="story-avatar">
                            </div>
                            <p class="story-name">David</p>
                        </div>
                        <div class="story-item" onclick="handleStoryClick(7)">
                            <div class="story-avatar-wrapper has-story">
                                <img src="https://placehold.co/100x100/6366F1/ffffff?text=O" alt="Olivia" class="story-avatar">
                            </div>
                            <p class="story-name">Olivia</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Share Your Story section -->
            <div class="card share-story-card">
                <h2 class="card-title">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3L9.27 9.27L3 12l6.27 2.73L12 21l2.73-6.27L21 12l-6.27-2.73L12 3z"></path><path d="M3 21L12 12"></path><path d="M21 3L12 12"></path></svg>
                    Share Your Story
                </h2>
                <p>What positive experience would you like to share with the community today?</p>
                <button class="btn-primary create-post-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                    Create Post
                </button>
            </div>

            <!-- Explore section -->
            <div class="card feed-card">
                <div class="card-header">
                    <h2 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                        Explore
                    </h2>
                    <button class="refresh-btn" data-explore-refresh>Refresh</button>
                </div>
                <div class="explore-filters">
                    <div class="filters-row">
                        <label class="sr-only" for="explore-search">Search posts</label>
                        <input id="explore-search" type="search" class="filter-search-input" placeholder="Search posts, people, topics..." />
                        <select class="filter-select filter-sort-select" aria-label="Sort posts">
                            <option value="latest">Latest</option>
                            <option value="top">Top</option>
                        </select>
                        <select class="filter-select filter-range-select" aria-label="Time range">
                            <option value="all">All time</option>
                            <option value="day">Today</option>
                            <option value="week">This week</option>
                            <option value="month">This month</option>
                        </select>
                    </div>
                    <div class="filter-chips" role="toolbar" aria-label="Filter by category">
                        <button class="chip" data-tag="all" aria-pressed="true">All</button>
                        <button class="chip" data-tag="Personal Experience" aria-pressed="false">Personal</button>
                        <button class="chip" data-tag="Inspiration" aria-pressed="false">Inspiration</button>
                        <button class="chip" data-tag="Technology" aria-pressed="false">Technology</button>
                    </div>
                </div>
                <div class="posts-container"></div>
            </div>
        </section>

        <!-- Right sidebar (Trending Topics) -->
        <aside class="trending-modern">
            <h2 class="trending-title">Trending Topics</h2>
            <ul class="trending-list">
                <li class="trending-item" data-modal-target="trending-modal">
                    <p class="topic">#EthicalHacking</p>
                    <span class="post-count">4.5k posts</span>
                </li>
                <li class="trending-item" data-modal-target="trending-modal">
                    <p class="topic">#ReactJS</p>
                    <span class="post-count">3.2k posts</span>
                </li>
                <li class="trending-item" data-modal-target="trending-modal">
                    <p class="topic">#CookingTips</p>
                    <span class="post-count">1.8k posts</span>
                </li>
                <li class="trending-item" data-modal-target="trending-modal">
                    <p class="topic">#FamilyTravel</p>
                    <span class="post-count">1.1k posts</span>
                </li>
            </ul>
        </aside>
    </main>

    <!-- Modals -->
    <div id="search-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Search</h3>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>Search functionality will be implemented here.</p>
            </div>
        </div>
    </div>

    <div id="notifications-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Notifications</h3>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <ul class="notification-list">
                    <li class="notification-item">
                        <span class="icon">👍</span>
                        <div>
                            <strong>John Doe</strong> liked your post.
                            <small class="text-slate-400">2 hours ago</small>
                        </div>
                    </li>
                    <li class="notification-item">
                        <span class="icon">💬</span>
                        <div>
                            <strong>Jane Smith</strong> commented on your post.
                            <small class="text-slate-400">3 hours ago</small>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div id="profile-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Profile Menu</h3>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <ul class="profile-menu">
                    <li class="profile-menu-item">View Profile</li>
                    <li class="profile-menu-item">Settings</li>
                    <li class="profile-menu-item">Log Out</li>
                </ul>
            </div>
        </div>
    </div>

    

    <div id="trending-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Trending Topics</h3>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>Trending topics details will be here.</p>
            </div>
        </div>
    </div>

    <div id="stories-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">All Stories</h3>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>A grid of all stories will be displayed here.</p>
            </div>
        </div>
    </div>


    <!-- Include essential JavaScript files for functionality -->
    <script src="/public/js/modal.js"></script>
    <script type="module" src="/public/js/utils.js"></script>
    <script type="module" src="/public/js/notification-system.js"></script>
    <script type="module" src="/public/js/firebase-config.js"></script>
    <script type="module" src="/public/js/authentication.js"></script>
    <script type="module" src="/public/js/core.js"></script>
    <script src="/public/js/navigation-enhanced.js"></script>
    <script src="/public/js/stories-enhanced.js"></script>
    <script src="/public/js/modern-interactions.js"></script>
    <script type="module" src="/public/js/posts.js"></script>
    <script type="module" src="/public/js/profile.js"></script>
    <script src="/public/js/modal-system.js"></script>
    <script src="/public/js/accessibility-preferences.js"></script>

    <script>
        // Story handlers
        function handleStoryClick(storyId) {
            console.log(`Story with ID ${storyId} clicked.`);
            // In a real application, you would navigate to the story or open a modal.
        }

        function handleAddStory() {
            console.log('Add new story clicked.');
            // In a real application, you would open an upload interface.
        }
    </script>
</body>
</html>
