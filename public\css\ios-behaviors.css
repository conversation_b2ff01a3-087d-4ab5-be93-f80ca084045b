/**
 * iOS-Specific Behaviors CSS
 * Styles for native iOS touch behaviors and interactions
 */

/* iOS Touch Feedback */
.ios-touch-active {
    opacity: 0.7 !important;
    transform: scale(0.98) !important;
    transition: opacity 0.1s ease, transform 0.1s ease !important;
}

/* iOS Button Press Animation */
button,
.btn,
.action-btn {
    transition: transform 0.1s ease, opacity 0.1s ease;
    -webkit-tap-highlight-color: transparent;
}

button:active,
.btn:active,
.action-btn:active {
    transform: scale(0.95);
    opacity: 0.8;
}

/* iOS Momentum Scrolling */
.ios-momentum-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* Disable iOS text selection on UI elements */
.ios-no-select {
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* Enable text selection for content */
.ios-text-selectable {
    -webkit-user-select: text;
    user-select: text;
    -webkit-touch-callout: default;
}

/* iOS-style focus states */
input:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color, #f59e0b);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

/* iOS-style scrollbar (WebKit) */
::-webkit-scrollbar {
    width: 3px;
    height: 3px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* iOS-style bounce prevention */
body {
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
}

/* Allow bounce on main content areas */
.main-content,
.feed-container,
#postsContainer {
    overscroll-behavior: auto;
    -webkit-overflow-scrolling: touch;
}

/* iOS-style modal animations */
.modal.ios-style {
    animation: iosModalSlideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosModalSlideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* iOS-style card interactions */
.post-card,
.story-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.post-card:active,
.story-card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* iOS-style navigation transitions */
.page-transition {
    animation: iosPageSlide 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosPageSlide {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* iOS-style loading states */
.ios-loading {
    position: relative;
    overflow: hidden;
}

.ios-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: iosShimmer 1.5s infinite;
}

@keyframes iosShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* iOS-style form elements */
input[type="text"],
input[type="email"],
input[type="password"],
textarea {
    -webkit-appearance: none;
    appearance: none;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
    transition: all 0.2s ease;
}

/* iOS-style switches/toggles */
.ios-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.ios-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ios-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 31px;
}

.ios-switch .slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ios-switch input:checked + .slider {
    background-color: var(--primary-color, #f59e0b);
}

.ios-switch input:checked + .slider:before {
    transform: translateX(20px);
}

/* iOS-style alerts and notifications */
.ios-alert {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 14px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: iosAlertScale 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosAlertScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* iOS-style safe area handling */
@supports (padding: max(0px)) {
    .ios-safe-area-top {
        padding-top: max(20px, env(safe-area-inset-top));
    }

    .ios-safe-area-bottom {
        padding-bottom: max(20px, env(safe-area-inset-bottom));
    }

    .ios-safe-area-left {
        padding-left: max(20px, env(safe-area-inset-left));
    }

    .ios-safe-area-right {
        padding-right: max(20px, env(safe-area-inset-right));
    }
}

/* iOS Keyboard Handling */
.keyboard-visible {
    padding-bottom: var(--keyboard-height, 0px);
}

.ios-input-focused {
    border-color: var(--primary-color, #f59e0b) !important;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* iOS keyboard toolbar */
.ios-keyboard-toolbar {
    position: fixed;
    bottom: var(--keyboard-height, 0px);
    left: 0;
    right: 0;
    height: 44px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    z-index: 9999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.keyboard-visible .ios-keyboard-toolbar {
    transform: translateY(0);
}

/* iOS input animations */
input, textarea {
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}

/* iOS-style placeholder */
input::placeholder,
textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

/* iOS-style autofill */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.1) inset !important;
    -webkit-text-fill-color: white !important;
}
