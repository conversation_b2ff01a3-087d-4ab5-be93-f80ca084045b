/**
 * iOS-Specific Behaviors CSS
 * Styles for native iOS touch behaviors and interactions
 */

/* iOS Touch Feedback */
.ios-touch-active {
    opacity: 0.7 !important;
    transform: scale(0.98) !important;
    transition: opacity 0.1s ease, transform 0.1s ease !important;
}

/* iOS Button Press Animation */
button,
.btn,
.action-btn {
    transition: transform 0.1s ease, opacity 0.1s ease;
    -webkit-tap-highlight-color: transparent;
}

button:active,
.btn:active,
.action-btn:active {
    transform: scale(0.95);
    opacity: 0.8;
}

/* iOS Momentum Scrolling */
.ios-momentum-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* Disable iOS text selection on UI elements */
.ios-no-select {
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* Enable text selection for content */
.ios-text-selectable {
    -webkit-user-select: text;
    user-select: text;
    -webkit-touch-callout: default;
}

/* iOS-style focus states */
input:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color, #f59e0b);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

/* iOS-style scrollbar (WebKit) */
::-webkit-scrollbar {
    width: 3px;
    height: 3px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* iOS-style bounce prevention */
body {
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
}

/* Allow bounce on main content areas */
.main-content,
.feed-container,
#postsContainer {
    overscroll-behavior: auto;
    -webkit-overflow-scrolling: touch;
}

/* iOS-style modal animations */
.modal.ios-style {
    animation: iosModalSlideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosModalSlideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* iOS-style card interactions */
.post-card,
.story-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.post-card:active,
.story-card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* iOS-style navigation transitions */
.page-transition {
    animation: iosPageSlide 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosPageSlide {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* iOS-style loading states */
.ios-loading {
    position: relative;
    overflow: hidden;
}

.ios-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: iosShimmer 1.5s infinite;
}

@keyframes iosShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* iOS-style form elements */
input[type="text"],
input[type="email"],
input[type="password"],
textarea {
    -webkit-appearance: none;
    appearance: none;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
    transition: all 0.2s ease;
}

/* iOS-style switches/toggles */
.ios-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.ios-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ios-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 31px;
}

.ios-switch .slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ios-switch input:checked + .slider {
    background-color: var(--primary-color, #f59e0b);
}

.ios-switch input:checked + .slider:before {
    transform: translateX(20px);
}

/* iOS-style alerts and notifications */
.ios-alert {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 14px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: iosAlertScale 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosAlertScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* iOS-style safe area handling */
@supports (padding: max(0px)) {
    .ios-safe-area-top {
        padding-top: max(20px, env(safe-area-inset-top));
    }

    .ios-safe-area-bottom {
        padding-bottom: max(20px, env(safe-area-inset-bottom));
    }

    .ios-safe-area-left {
        padding-left: max(20px, env(safe-area-inset-left));
    }

    .ios-safe-area-right {
        padding-right: max(20px, env(safe-area-inset-right));
    }
}

/* iOS Keyboard Handling */
.keyboard-visible {
    padding-bottom: var(--keyboard-height, 0px);
}

.ios-input-focused {
    border-color: var(--primary-color, #f59e0b) !important;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* iOS keyboard toolbar */
.ios-keyboard-toolbar {
    position: fixed;
    bottom: var(--keyboard-height, 0px);
    left: 0;
    right: 0;
    height: 44px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    z-index: 9999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.keyboard-visible .ios-keyboard-toolbar {
    transform: translateY(0);
}

/* iOS input animations */
input, textarea {
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}

/* iOS-style placeholder */
input::placeholder,
textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

/* iOS-style autofill */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.1) inset !important;
    -webkit-text-fill-color: white !important;
}

/* iOS Share Button Styles */
.ios-share-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-color, white);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ios-share-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.ios-share-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.2);
}

.ios-share-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
}

/* iOS Toast Notifications */
.ios-toast {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ios-toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.ios-toast-success {
    background: rgba(52, 199, 89, 0.9);
}

.ios-toast-error {
    background: rgba(255, 59, 48, 0.9);
}

.ios-toast-info {
    background: rgba(0, 122, 255, 0.9);
}

/* Post Actions Container */
.post-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.story-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

/* iOS Share Animation */
@keyframes iosSharePulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.ios-share-btn.sharing {
    animation: iosSharePulse 0.3s ease;
}

/* iOS Pull-to-Refresh Styles */
.ios-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%) translateY(-60px);
    opacity: 0;
    z-index: 100;
    transition: opacity 0.2s ease;
}

.ios-refresh-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ios-refresh-icon {
    width: 24px;
    height: 24px;
    stroke: white;
    transition: transform 0.2s ease;
}

.ios-refresh-text {
    color: white;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.ios-refresh-indicator.ready .ios-refresh-icon {
    stroke: var(--primary-color, #f59e0b);
}

.ios-refresh-indicator.ready .ios-refresh-text {
    color: var(--primary-color, #f59e0b);
}

.ios-refresh-indicator.refreshing .ios-refresh-icon {
    stroke: var(--primary-color, #f59e0b);
}

/* iOS Refresh Spinner Animation */
@keyframes iosRefreshSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Pull-to-refresh container adjustments */
.main-content,
.feed-container {
    position: relative;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Prevent overscroll on refresh containers */
.ios-refresh-container {
    overscroll-behavior-y: none;
}

/* iOS Navigation Patterns */
.swipe-back-active {
    overflow-x: hidden;
}

.swipe-back-active .page-content,
.swipe-back-active main,
.swipe-back-active .main-content {
    will-change: transform;
}

/* iOS Page Transitions */
.transition-slide .page-content,
.transition-slide main,
.transition-slide .main-content {
    animation: iosSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes iosSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0.8;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.transition-fade .page-content,
.transition-fade main,
.transition-fade .main-content {
    animation: iosFadeIn 0.3s ease;
}

@keyframes iosFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* iOS Modal Transitions */
.modal {
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal.show {
    transform: translateY(0);
}

/* iOS Navigation Bar */
.ios-nav-bar {
    position: sticky;
    top: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 100;
}

.ios-nav-title {
    font-size: 17px;
    font-weight: 600;
    color: white;
    text-align: center;
    flex: 1;
}

.ios-nav-back {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--primary-color, #f59e0b);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    margin: -8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.ios-nav-back:hover {
    background: rgba(255, 255, 255, 0.1);
}

.ios-nav-back svg {
    width: 16px;
    height: 16px;
}

/* iOS Tab Bar */
.ios-tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
    display: flex;
    justify-content: space-around;
    z-index: 1000;
}

.ios-tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s ease;
    min-width: 60px;
}

.ios-tab-item.active {
    color: var(--primary-color, #f59e0b);
}

.ios-tab-item svg {
    width: 24px;
    height: 24px;
}

/* iOS Swipe Back Visual Feedback */
.swipe-back-indicator {
    position: fixed;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60px;
    background: var(--primary-color, #f59e0b);
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.swipe-back-active .swipe-back-indicator {
    opacity: 1;
}
